#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTabWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

# 现代化UI主题样式表
modern_stylesheet = """
    QWidget {
        background-color: #fafbfc;
        color: #1e293b;
        font-size: 10pt;
        font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
    }
    
    QMainWindow {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #fafbfc, stop: 1 #f1f5f9);
    }
    
    QTabWidget::pane {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        background: white;
        margin-top: -1px;
    }
    
    QTabBar::tab {
        background: #f1f5f9;
        color: #64748b;
        border: 1px solid #e2e8f0;
        border-bottom: none;
        padding: 8px 16px;
        margin-right: 2px;
        min-width: 80px;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
    }
    
    QTabBar::tab:selected {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #000000;
        border: 1px solid #667eea;
        border-top: 2px solid #667eea;
        border-bottom: 1px solid #667eea;
        margin-bottom: -1px;
        font-weight: bold;
    }
    
    QTabBar::tab:!selected:hover {
        background: #e2e8f0;
        color: #475569;
    }
    
    QPushButton {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #ffffff, stop: 1 #f1f5f9);
        border: 2px solid #e2e8f0;
        color: #475569;
        padding: 8px 16px;
        min-height: 20px;
        min-width: 80px;
        border-radius: 8px;
        font-weight: 500;
    }
    
    QPushButton:hover {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #f8fafc, stop: 1 #e2e8f0);
        border-color: #cbd5e1;
        transform: translateY(-1px);
    }
    
    QPushButton:pressed {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #e2e8f0, stop: 1 #cbd5e1);
        transform: translateY(0px);
    }
"""

class TestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()
    
    def initUI(self):
        self.setWindowTitle('🎬 UI测试 - 现代化界面')
        self.setGeometry(100, 50, 1000, 800)
        self.setMinimumSize(900, 700)
        self.setMaximumSize(1200, 900)
        
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(12)
        
        # 顶部连接区域
        connection_widget = QWidget()
        connection_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8fafc);
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                padding: 8px;
            }
        """)
        connection_layout = QHBoxLayout(connection_widget)
        connection_layout.setContentsMargins(15, 10, 15, 10)
        connection_layout.setSpacing(15)
        
        status_label = QLabel('🔴 未连接')
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                font-weight: bold;
                color: #dc2626;
                background: transparent;
                padding: 5px 10px;
            }
        """)
        
        connect_button = QPushButton('🔗 连接到 OBS')
        connect_button.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11pt;
                min-width: 120px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
                transform: translateY(-1px);
            }
        """)
        
        quick_start_button = QPushButton('🚀 一键启动')
        quick_start_button.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 11pt;
                min-width: 120px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            }
        """)
        
        connection_layout.addWidget(status_label, 1)
        connection_layout.addWidget(connect_button)
        connection_layout.addWidget(quick_start_button)
        main_layout.addWidget(connection_widget)
        
        # Tab控件
        tab_widget = QTabWidget()
        
        # 测试Tab
        test_tab1 = QWidget()
        test_layout1 = QVBoxLayout(test_tab1)
        test_layout1.addWidget(QLabel("这是第一个Tab页面"))
        test_layout1.addWidget(QPushButton("测试按钮1"))
        tab_widget.addTab(test_tab1, "🎬 视频去重")
        
        test_tab2 = QWidget()
        test_layout2 = QVBoxLayout(test_tab2)
        test_layout2.addWidget(QLabel("这是第二个Tab页面"))
        test_layout2.addWidget(QPushButton("测试按钮2"))
        tab_widget.addTab(test_tab2, "🎵 音频去重")
        
        test_tab3 = QWidget()
        test_layout3 = QVBoxLayout(test_tab3)
        test_layout3.addWidget(QLabel("这是第三个Tab页面"))
        test_layout3.addWidget(QPushButton("测试按钮3"))
        tab_widget.addTab(test_tab3, "⚡ 爆闪播放器")
        
        main_layout.addWidget(tab_widget)
        
        # 底部按钮区域
        buttons_layout = QHBoxLayout()
        
        restore_btn = QPushButton("🔄 恢复默认设置")
        restore_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 10pt;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
            }
        """)
        buttons_layout.addWidget(restore_btn)
        buttons_layout.addStretch()
        
        author_label = QLabel("👨‍💻 作者：阿昊\n📞 有问题随时联系\n💬 WX：LT865432")
        author_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        author_label.setStyleSheet("""
            QLabel {
                color: #64748b;
                font-size: 9pt;
                font-weight: 500;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8fafc, stop: 1 #e2e8f0);
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px 12px;
            }
        """)
        buttons_layout.addWidget(author_label)
        
        main_layout.addSpacing(10)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setStyleSheet(modern_stylesheet)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())
