import sys
import os
import ctypes
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QIcon
import main_module

# 兼容 PyInstaller 单文件和开发环境的资源路径
def resource_path(relative_path):
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath(os.path.dirname(__file__)), relative_path)

if __name__ == '__main__':
    app_id = 'com.haohnb.obscontroller.1.0'
    try:
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
    except Exception as e:
        print(f"设置 AppUserModelID 失败: {e}")

    app = QApplication(sys.argv)

    # 应用现代化样式表
    app.setStyleSheet(main_module.light_stylesheet)

    icon_path = resource_path('obs2.ico')
    if not os.path.exists(icon_path):
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'obs2.ico')
    if os.path.exists(icon_path):
        try:
            app_icon = QIcon(icon_path)
            app.setWindowIcon(app_icon)
            if hasattr(ctypes, 'windll'):
                myappid = app_id
                ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
            print(f"成功加载图标: {icon_path}")
        except Exception as e:
            print(f"设置图标失败: {e}")
    else:
        print(f"找不到图标文件: {icon_path}")

    main_window = main_module.MainWindow()
    if os.path.exists(icon_path):
        main_window.setWindowIcon(QIcon(icon_path))
    main_window.show()
    main_window.check_and_handle_activation()
    sys.exit(app.exec_())